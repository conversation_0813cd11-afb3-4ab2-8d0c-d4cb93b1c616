.tagline {
  display: flex;
  gap: 15px;
  padding: 15px 5px;
  background-color: #64b100;
  text-align: center;
}

.tagline div {
  color: rgb(255, 255, 255);
  padding: 10px 20px;
  border-radius: 30px;
  font-weight: 500;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.tagline div:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

@media (max-width: 1200px) {
  .tagline {
    padding: 25px 15px;
  }

  .tagline div {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}

@media (max-width: 992px) {
  .tagline {
    padding: 20px 10px;
    gap: 10px;
  }

  .tagline div {
    padding: 7px 14px;
    font-size: 0.85rem;
  }
}

@media (max-width: 768px) {
  .about {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .about-images {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .tagline {
    padding: 15px 10px;
    gap: 8px;
  }

  .tagline div {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 576px) {
  .tagline {
    padding: 12px 8px;
    gap: 6px;
  }

  .tagline div {
    padding: 5px 10px;
    font-size: 0.75rem;
    border-radius: 20px;
  }
}

@media (max-width: 768px) {
  .register-container {
    padding: 2rem;
    border-radius: 16px;
  }

  .login-left {
    max-width: 0vw;
  }

  .register-container h1 {
    font-size: 1.3rem;
  }

  .register-subtitle {
    font-size: 0.8rem;
  }

  .register-container input,
  .register-container button {
    font-size: 0.8rem;
    padding: 8px 16px;
  }

  .toggle-password {
    right: 6px;
    font-size: 0.75rem;
  }

  .toggle-password i {
    font-size: 0.7rem;
  }
}

@media (max-width: 360px) {
  .form-label {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.75rem;
    background-color: #ffffff;
  }

  .register-container {
    padding: 1.5rem 1rem;
    border-radius: 12px;
    box-shadow: none;
    background-color: #ffffff;
    max-width: 100%;
    margin: 0 auto;
  }

  .register-container h1 {
    font-size: 1.4rem;
    text-align: center;
    margin-bottom: 0.25rem;
  }

  .register-subtitle {
    font-size: 0.78rem;
    color: #555;
    margin-bottom: 1.25rem;
    text-align: center;
  }

  .form-label {
    font-size: 0.75rem;
    color: #333;
    margin-bottom: 0.3rem;
  }

  .register-container input {
    font-size: 0.8rem;
    padding: 10px 12px;
    border-radius: 10px;
    background-color: #f5f5f5;
  }

  .password-wrapper {
    align-items: center;
  }

  .toggle-password {
    font-size: 0.7rem;
    right: 6px;
  }

  .toggle-password i {
    font-size: 0.75rem;
  }

  .register-container button {
    font-size: 0.85rem;
    padding: 10px;
    border-radius: 25px;
  }

  .login-link {
    font-size: 0.75rem;
    margin-top: 1rem;
    text-align: center;
  }

  .login-link a {
    color: #2483ff;
    text-decoration: underline;
  }

  .error {
    font-size: 0.7rem;
    margin-bottom: 0.3rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.75rem;
    background-color: #ffffff;
  }

  .login-container {
    padding: 1.5rem 1rem;
    border-radius: 12px;
    box-shadow: none;
    background-color: #ffffff;
    max-width: 100%;
    margin: 0 auto;
  }

  .login-container h1 {
    font-size: 1.4rem;
    text-align: center;
    margin-bottom: 0.25rem;
  }

  .login-subtitle {
    font-size: 0.78rem;
    color: #555;
    margin-bottom: 1.25rem;
    text-align: center;
  }

  .form-label {
    font-size: 0.75rem;
    color: #333;
    margin-bottom: 0.3rem;
  }

  .login-container input {
    font-size: 0.8rem;
    padding: 10px 12px;
    border-radius: 10px;
    background-color: #f5f5f5;
  }

  .password-wrapper {
    align-items: center;
  }

  .toggle-password {
    font-size: 0.7rem;
    right: 6px;
  }

  .toggle-password i {
    font-size: 0.75rem;
  }

  .login-container button {
    font-size: 0.85rem;
    padding: 10px;
    border-radius: 25px;
  }

  .register-link {
    font-size: 0.75rem;
    margin-top: 1rem;
    text-align: center;
  }

  .register-link a {
    color: #2483ff;
    text-decoration: underline;
  }

  .error {
    font-size: 0.7rem;
    margin-bottom: 0.3rem;
  }
}
