<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Picture Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .profile-picture-demo {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        .author-avatar {
            border-radius: 50%;
            object-fit: cover;
        }
        .author-avatar.small {
            width: 40px;
            height: 40px;
        }
        .author-avatar.medium {
            width: 80px;
            height: 80px;
        }
        .author-avatar.large {
            width: 120px;
            height: 120px;
        }
        .fallback-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            border-radius: 50%;
        }
        .fallback-avatar.small {
            width: 40px;
            height: 40px;
            font-size: 16px;
        }
        .fallback-avatar.medium {
            width: 80px;
            height: 80px;
            font-size: 24px;
        }
        .fallback-avatar.large {
            width: 120px;
            height: 120px;
            font-size: 36px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Profile Picture Functionality Test</h1>
        
        <div class="test-section">
            <h3>1. Profile Picture Service Test</h3>
            <p>Testing the profile picture service methods:</p>
            <button onclick="testProfilePictureService()">Test Profile Picture Service</button>
            <div id="service-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. Profile Picture URL Generation</h3>
            <p>Testing URL generation for different profile picture formats:</p>
            <button onclick="testUrlGeneration()">Test URL Generation</button>
            <div id="url-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. Fallback Avatar Creation</h3>
            <p>Testing fallback avatar creation for different sizes:</p>
            <div class="profile-picture-demo">
                <div id="fallback-small"></div>
                <div id="fallback-medium"></div>
                <div id="fallback-large"></div>
            </div>
            <button onclick="testFallbackAvatars()">Generate Fallback Avatars</button>
        </div>

        <div class="test-section">
            <h3>4. Profile Picture Element Creation</h3>
            <p>Testing complete profile picture element creation:</p>
            <div class="profile-picture-demo">
                <div id="profile-element-test"></div>
            </div>
            <button onclick="testProfilePictureElement()">Test Profile Picture Element</button>
        </div>

        <div class="test-section">
            <h3>5. Navigation Bar Profile Picture</h3>
            <p>Testing navigation bar with profile picture:</p>
            <div id="navbar-test"></div>
            <button onclick="testNavigationBar()">Test Navigation Bar</button>
        </div>
    </div>

    <script type="module">
        // Import the profile picture service
        import profilePictureService from './src/scripts/data/profile-picture-service.js';
        import { NavigationBar } from './src/scripts/components/NavigationBar.js';

        // Make functions available globally for button onclick
        window.testProfilePictureService = function() {
            const resultDiv = document.getElementById('service-test-result');
            resultDiv.style.display = 'block';
            
            try {
                // Test basic service methods
                const testUrl = profilePictureService.getProfilePictureUrl('test-image.jpg');
                const fallbackHtml = profilePictureService.createFallbackAvatar('TestUser', 'medium');
                
                if (testUrl && fallbackHtml) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        <strong>✓ Success!</strong><br>
                        Generated URL: ${testUrl}<br>
                        Fallback HTML generated successfully
                    `;
                } else {
                    throw new Error('Service methods returned null or undefined');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<strong>✗ Error:</strong> ${error.message}`;
            }
        };

        window.testUrlGeneration = function() {
            const resultDiv = document.getElementById('url-test-result');
            resultDiv.style.display = 'block';
            
            try {
                const tests = [
                    { input: 'profile-123.jpg', expected: 'should contain profile-123.jpg' },
                    { input: 'https://example.com/image.jpg', expected: 'should return as-is' },
                    { input: '/api/account/profile-picture/test.jpg', expected: 'should extract filename' },
                    { input: null, expected: 'should return null' }
                ];
                
                let results = [];
                tests.forEach(test => {
                    const result = profilePictureService.getProfilePictureUrl(test.input);
                    results.push(`Input: ${test.input} → Output: ${result}`);
                });
                
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `<strong>✓ URL Generation Tests:</strong><br>${results.join('<br>')}`;
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<strong>✗ Error:</strong> ${error.message}`;
            }
        };

        window.testFallbackAvatars = function() {
            try {
                const smallDiv = document.getElementById('fallback-small');
                const mediumDiv = document.getElementById('fallback-medium');
                const largeDiv = document.getElementById('fallback-large');
                
                smallDiv.innerHTML = profilePictureService.createFallbackAvatar('John', 'small');
                mediumDiv.innerHTML = profilePictureService.createFallbackAvatar('Jane', 'medium');
                largeDiv.innerHTML = profilePictureService.createFallbackAvatar('Bob', 'large');
            } catch (error) {
                alert('Error creating fallback avatars: ' + error.message);
            }
        };

        window.testProfilePictureElement = function() {
            try {
                const testDiv = document.getElementById('profile-element-test');
                
                // Test with a fake profile picture URL
                const profileElement = profilePictureService.createProfilePictureElement(
                    'test-profile.jpg', 
                    'TestUser', 
                    'medium'
                );
                
                testDiv.innerHTML = profileElement;
            } catch (error) {
                alert('Error creating profile picture element: ' + error.message);
            }
        };

        window.testNavigationBar = function() {
            try {
                const testDiv = document.getElementById('navbar-test');
                
                const navbar = new NavigationBar({
                    currentPath: '/test',
                    userInitial: 'T',
                    username: 'TestUser',
                    profilePictureUrl: 'test-profile.jpg',
                    showProfile: true
                });
                
                testDiv.innerHTML = navbar.render();
                
                // Bind events after rendering
                setTimeout(() => {
                    navbar.bindEvents();
                }, 100);
            } catch (error) {
                alert('Error creating navigation bar: ' + error.message);
            }
        };
    </script>
</body>
</html>
