API Schema Documentation - Updated dengan Progress Terbaru

=== PROJECT STATUS ===
✅ COMPLETED: Full REST API with Node.js/Express + MongoDB
✅ COMPLETED: Authentication system with JWT
✅ COMPLETED: Image upload functionality (GridFS)
✅ COMPLETED: Profile picture management
✅ COMPLETED: Learning content management system
✅ COMPLETED: Category management
✅ COMPLETED: Interactive API documentation
✅ COMPLETED: MongoDB Atlas integration with optimization
✅ COMPLETED: Production-ready deployment configuration
✅ COMPLETED: Comprehensive error handling and logging
✅ COMPLETED: Rate limiting and security features

=== TECHNICAL STACK ===
- Backend: Node.js + Express.js
- Database: MongoDB Atlas (Cloud) with GridFS for file storage
- Authentication: JWT with refresh tokens
- File Upload: Multer + GridFS for images
- Documentation: Interactive HTML documentation with source code
- Security: Helmet, CORS, Rate limiting, Input validation
- Logging: Winston with structured logging
- Environment: Development/Production configurations

=== DATABASE CONFIGURATION ===
- MongoDB Atlas: Fully configured and optimized
- Connection Pooling: maxPoolSize: 10, minPoolSize: 2
- Indexes: Optimized for all collections (User, Post, Comment, Category, Learning)
- GridFS: For image storage (posts, profiles, learning content)
- Performance: Database statistics monitoring and optimization

=== API ENDPOINTS SCHEMA ===

{
  "endpoints": {
    "authentication": {
      "register": {
        "method": "POST",
        "url": "/api/auth/register",
        "body": {
          "username": "string",
          "email": "string",
          "password": "string"
        },
        "response": {
          "user": {
            "id": "string",
            "username": "string",
            "email": "string",
            "profilePictureUrl": "string",
            "createdAt": "timestamp"
          },
          "token": "string"
        }
      },
      "login": {
        "method": "POST",
        "url": "/api/auth/login",
        "body": {
          "email": "string",
          "password": "string"
        },
        "response": {
          "user": {
            "id": "string",
            "username": "string",
            "email": "string",
            "profilePictureUrl": "string"
          },
          "token": "string"
        }
      },
      "logout": {
        "method": "POST",
        "url": "/api/auth/logout",
        "auth": "required"
      },
      "getUser": {
        "method": "GET",
        "url": "/api/auth/user",
        "auth": "required",
        "response": {
          "id": "string",
          "username": "string",
          "email": "string",
          "profilePictureUrl": "string",
          "createdAt": "timestamp"
        }
      },
      "refreshToken": {
        "method": "POST",
        "url": "/api/auth/refresh",
        "body": {
          "refreshToken": "string"
        },
        "response": {
          "token": "string",
          "refreshToken": "string"
        }
      }
    },
    "posts": {
      "getAllPosts": {
        "method": "GET",
        "url": "/api/posts",
        "query": {
          "page": "number",
          "limit": "number",
          "sort": "string"
        }
      },
      "getPostById": {"method": "GET", "url": "/api/posts/{id}"},
      "createPost": {
        "method": "POST",
        "url": "/api/posts",
        "auth": "required",
        "contentType": "multipart/form-data",
        "body": {
          "title": "string (required)",
          "content": "string (required)",
          "image": "file (optional, max 5MB, jpg/png/gif/webp)"
        },
        "response": {
          "success": "boolean",
          "data": {
            "id": "string",
            "title": "string",
            "content": "string",
            "imageUrl": "string",
            "userId": {
              "id": "string",
              "username": "string"
            },
            "createdAt": "timestamp",
            "updatedAt": "timestamp"
          }
        }
      },
      "updatePost": {
        "method": "PUT",
        "url": "/api/posts/{id}",
        "auth": "required",
        "ownerOnly": true,
        "contentType": "multipart/form-data",
        "body": {
          "title": "string (optional)",
          "content": "string (optional)",
          "image": "file (optional, max 5MB, jpg/png/gif/webp)"
        },
        "response": {
          "success": "boolean",
          "data": {
            "id": "string",
            "title": "string",
            "content": "string",
            "imageUrl": "string",
            "userId": "object",
            "createdAt": "timestamp",
            "updatedAt": "timestamp"
          }
        }
      },
      "getPostImage": {
        "method": "GET",
        "url": "/api/posts/image/{filename}",
        "auth": "public",
        "response": "image/binary (with proper Content-Type headers)"
      },
      "deletePost": {
        "method": "DELETE",
        "url": "/api/posts/{id}",
        "auth": "required",
        "ownerOnly": true
      }
    },
    "comments": {
      "getComments": {"method": "GET", "url": "/api/posts/{id}/comments"},
      "createComment": {
        "method": "POST",
        "url": "/api/posts/{id}/comments",
        "auth": "required",
        "body": {
          "content": "string"
        },
        "response": {
          "id": "string",
          "content": "string",
          "userId": "string",
          "postId": "string",
          "createdAt": "timestamp"
        }
      },
      "updateComment": {
        "method": "PUT",
        "url": "/api/posts/{id}/comments/{commentId}",
        "auth": "required",
        "ownerOnly": true,
        "body": {
          "content": "string"
        }
      },
      "deleteComment": {
        "method": "DELETE",
        "url": "/api/posts/{id}/comments/{commentId}",
        "auth": "required",
        "ownerOnly": true
      }
    },
    "account": {
      "getAccount": {
        "method": "GET",
        "url": "/api/account",
        "auth": "required",
        "response": {
          "id": "string",
          "username": "string",
          "email": "string",
          "profilePictureUrl": "string",
          "createdAt": "timestamp"
        }
      },
      "updateAccount": {
        "method": "PUT",
        "url": "/api/account",
        "auth": "required",
        "body": {
          "username": "string",
          "email": "string"
        },
        "response": {
          "id": "string",
          "username": "string",
          "email": "string",
          "profilePictureUrl": "string",
          "createdAt": "timestamp"
        }
      },
      "deleteAccount": {
        "method": "DELETE",
        "url": "/api/account",
        "auth": "required"
      },
      "uploadProfilePicture": {
        "method": "POST",
        "url": "/api/account/profile-picture",
        "auth": "required",
        "body": {
          "profilePicture": "multipart/form-data"
        },
        "response": {
          "id": "string",
          "username": "string",
          "email": "string",
          "profilePictureUrl": "string",
          "createdAt": "timestamp"
        }
      },
      "getProfilePicture": {
        "method": "GET",
        "url": "/api/account/profile-picture/{filename}",
        "response": "image/binary"
      },
      "deleteProfilePicture": {
        "method": "DELETE",
        "url": "/api/account/profile-picture",
        "auth": "required",
        "response": {
          "id": "string",
          "username": "string",
          "email": "string",
          "profilePictureUrl": "string",
          "createdAt": "timestamp"
        }
      }
    },
    "categories": {
      "getAllCategories": {
        "method": "GET",
        "url": "/api/categories",
        "query": {
          "active": "boolean"
        },
        "response": {
          "success": "boolean",
          "count": "number",
          "data": [{
            "id": "string",
            "name": "string",
            "description": "string",
            "color": "string",
            "icon": "string",
            "isActive": "boolean",
            "createdAt": "timestamp"
          }]
        }
      },
      "getCategory": {
        "method": "GET",
        "url": "/api/categories/{id}"
      },
      "createCategory": {
        "method": "POST",
        "url": "/api/categories",
        "auth": "required",
        "body": {
          "name": "string",
          "description": "string",
          "color": "string",
          "icon": "string"
        }
      },
      "updateCategory": {
        "method": "PUT",
        "url": "/api/categories/{id}",
        "auth": "required"
      },
      "deleteCategory": {
        "method": "DELETE",
        "url": "/api/categories/{id}",
        "auth": "required"
      }
    },
    "learning": {
      "getAllLearning": {
        "method": "GET",
        "url": "/api/learning",
        "query": {
          "page": "number",
          "limit": "number",
          "sort": "string",
          "category": "string",
          "difficulty": "string",
          "search": "string",
          "published": "boolean"
        },
        "response": {
          "success": "boolean",
          "count": "number",
          "pagination": {
            "total": "number",
            "page": "number",
            "limit": "number",
            "pages": "number"
          },
          "data": [{
            "id": "string",
            "title": "string",
            "content": "string",
            "summary": "string",
            "difficulty": "string",
            "estimatedTime": "number",
            "imageUrl": "string",
            "categories": ["object"],
            "userId": "object",
            "isPublished": "boolean",
            "viewCount": "number",
            "createdAt": "timestamp"
          }]
        }
      },
      "getLearning": {
        "method": "GET",
        "url": "/api/learning/{id}"
      },
      "createLearning": {
        "method": "POST",
        "url": "/api/learning",
        "auth": "required",
        "contentType": "multipart/form-data",
        "body": {
          "title": "string (required)",
          "content": "string (required, long text)",
          "summary": "string (optional)",
          "difficulty": "string (beginner/intermediate/advanced)",
          "estimatedTime": "number (minutes)",
          "categories": "array (category IDs)",
          "image": "file (optional, max 5MB, jpg/png/gif/webp)",
          "isPublished": "boolean (default: false)"
        },
        "response": {
          "success": "boolean",
          "data": {
            "id": "string",
            "title": "string",
            "content": "string",
            "summary": "string",
            "difficulty": "string",
            "estimatedTime": "number",
            "imageUrl": "string",
            "categories": ["object"],
            "userId": "object",
            "isPublished": "boolean",
            "viewCount": "number",
            "createdAt": "timestamp"
          }
        }
      },
      "updateLearning": {
        "method": "PUT",
        "url": "/api/learning/{id}",
        "auth": "required",
        "ownerOnly": true,
        "contentType": "multipart/form-data",
        "body": {
          "title": "string (optional)",
          "content": "string (optional)",
          "summary": "string (optional)",
          "difficulty": "string (optional)",
          "estimatedTime": "number (optional)",
          "categories": "array (optional)",
          "image": "file (optional)",
          "isPublished": "boolean (optional)"
        },
        "response": {
          "success": "boolean",
          "data": "learning object"
        }
      },
      "deleteLearning": {
        "method": "DELETE",
        "url": "/api/learning/{id}",
        "auth": "required",
        "ownerOnly": true
      },
      "getLearningImage": {
        "method": "GET",
        "url": "/api/learning/image/{filename}",
        "response": "image/binary"
      },
      "getLearningByCategory": {
        "method": "GET",
        "url": "/api/learning/category/{categoryId}"
      },
      "bookmarkLearning": {
        "method": "POST",
        "url": "/api/learning/{id}/bookmark",
        "auth": "required"
      },
      "removeBookmark": {
        "method": "DELETE",
        "url": "/api/learning/{id}/bookmark",
        "auth": "required"
      },
      "getBookmarks": {
        "method": "GET",
        "url": "/api/learning/bookmarks",
        "auth": "required"
      },
      "markAsCompleted": {
        "method": "POST",
        "url": "/api/learning/{id}/complete",
        "auth": "required"
      },
      "updateProgress": {
        "method": "PUT",
        "url": "/api/learning/{id}/progress",
        "auth": "required",
        "body": {
          "readingProgress": "number"
        }
      },
      "getUserProgress": {
        "method": "GET",
        "url": "/api/learning/progress",
        "auth": "required",
        "query": {
          "page": "number",
          "limit": "number",
          "completed": "boolean"
        }
      }
    },
    "system": {
      "health": {
        "method": "GET",
        "url": "/health",
        "auth": "public",
        "response": {
          "status": "string (UP/DOWN)",
          "environment": "string",
          "timestamp": "string"
        }
      },
      "apiStatus": {
        "method": "GET",
        "url": "/api/status",
        "auth": "public",
        "response": {
          "success": "boolean",
          "message": "string",
          "version": "string",
          "environment": "string",
          "timestamp": "string"
        }
      },
      "apiInfo": {
        "method": "GET",
        "url": "/api",
        "auth": "public",
        "response": {
          "success": "boolean",
          "message": "string",
          "version": "string",
          "environment": "string",
          "endpoints": "object (all available endpoints)"
        }
      }
    }
  }
}

=== DOCUMENTATION ENDPOINTS ===
- Main Documentation: GET / (index.html)
- API Documentation: GET /api-docs (interactive API docs with source code)
- Setup Documentation: GET /docs (docs.html)

=== SECURITY FEATURES ===
- JWT Authentication with refresh tokens
- Rate limiting: 100 requests per 15 minutes
- CORS configuration
- Helmet security headers
- Input validation and sanitization
- File upload restrictions (5MB max, specific file types)
- Owner-only access for sensitive operations

=== FILE STORAGE ===
- GridFS for MongoDB file storage
- Supported formats: JPG, PNG, GIF, WebP
- Maximum file size: 5MB
- Automatic file cleanup on deletion
- Optimized image serving with proper Content-Type headers

=== DATABASE OPTIMIZATION ===
- Indexes on all searchable fields
- Text search indexes for posts and learning content
- Connection pooling (10 max, 2 min connections)
- Automatic retry logic with graceful degradation
- Database statistics monitoring

=== DEPLOYMENT CONFIGURATION ===
- Environment-based configuration (.env, .env.production)
- Railway deployment ready
- MongoDB Atlas integration
- Graceful shutdown handling
- Process monitoring and error logging
- Health check endpoints for monitoring

=== API TESTING ===
- Interactive documentation with clickable endpoints
- Source code examples for each endpoint
- Request/response examples
- Authentication flow testing
- File upload testing interface

=== PERFORMANCE FEATURES ===
- Response compression
- Request logging with structured data
- Error tracking and monitoring
- Database query optimization
- Efficient pagination
- Caching headers for static content

=== MONITORING & LOGGING ===
- Winston logger with multiple levels
- Request/response logging
- Error tracking with stack traces
- Database connection monitoring
- Performance metrics logging
- Graceful error handling

=== PRODUCTION READINESS ===
✅ Environment configuration
✅ Security best practices
✅ Error handling and logging
✅ Database optimization
✅ File upload management
✅ API documentation
✅ Health monitoring
✅ Deployment configuration
✅ Rate limiting and CORS
✅ Input validation