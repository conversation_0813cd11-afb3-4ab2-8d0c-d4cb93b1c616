.services {
  padding: 40px 40px 60px 60px;
  margin-bottom: 18px;
  text-align: left;
}

.services h3 {
  color: #7ac142;
  font-weight: 500;
  margin-bottom: 8px;
  margin-top: 10px;
}

.services h2 {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 40px;
  color: #333;
}

.service-grid {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
  align-items: stretch;
}

.card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  padding: 16px;
  flex: 1 1 calc(25% - 15px);
  min-width: 200px;
  max-width: 280px;
  text-align: left;
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 24px rgba(0, 0, 0, 0.15);
}

.img-placeholder.small {
  width: 48px;
  height: 48px;
  background-color: #7ac142;
  border-radius: 50%;
  margin-bottom: 16px;
}

.card h4 {
  font-size: 18px;
  margin: 8px 0;
  color: #222;
}

.card p {
  font-size: 14px;
  color: #666;
}

.card img.card-image {
  width: 100%;
  margin-top: 16px;
  border-radius: 12px;
  object-fit: cover;
  height: 120px;
}

@media (min-width: 1200px) {
  .card {
    flex: 1 1 calc(25% - 15px);
    min-width: 220px;
    max-width: 300px;
  }
}

/* Medium screens (tablet): 2 cards per row */
@media (max-width: 1199px) and (min-width: 768px) {
  .services {
    padding: 50px 30px;
  }

  .services h2 {
    font-size: 24px;
    margin-bottom: 35px;
  }

  .service-grid {
    gap: 18px;
  }

  .card {
    flex: 1 1 calc(50% - 9px);
    min-width: 280px;
    max-width: none;
  }
}

@media (max-width: 768px) {
  .services {
    padding: 40px 20px;
    text-align: center;
  }

  .services h3 {
    font-size: 16px;
    margin-top: 8px;
  }

  .services h2 {
    font-size: 22px;
    margin-bottom: 25px;
  }

  .service-grid {
    gap: 16px;
  }

  .card {
    flex: 1 1 100%;
    min-width: auto;
    max-width: 400px;
    margin: 0 auto;
    padding: 14px;
  }

  .card h4 {
    font-size: 16px;
  }

  .img-placeholder.small {
    width: 40px;
    height: 40px;
    margin: 0 auto 12px;
  }
}

@media (max-width: 576px) {
  .services {
    padding: 30px 15px;
  }

  .services h3 {
    margin-top: 6px;
  }

  .services h2 {
    margin-bottom: 20px;
  }

  .service-grid {
    gap: 14px;
  }

  .card {
    padding: 12px;
    max-width: 350px;
  }

  .card h4 {
    font-size: 15px;
  }

  .card p {
    font-size: 13px;
  }

  .card img.card-image {
    height: 100px;
  }
}

@media (max-width: 480px) {
  .services h2 {
    font-size: 20px;
    margin-bottom: 18px;
  }

  .service-grid {
    gap: 12px;
  }

  .card {
    max-width: 300px;
  }

  .img-placeholder.small {
    width: 36px;
    height: 36px;
  }

  .card h4 {
    font-size: 14px;
  }

  .card p {
    font-size: 12px;
  }

  .card img.card-image {
    height: 90px;
    margin-top: 12px;
  }
}
