.home-container {
  font-family: "Poppins", sans-serif;
  margin: 0;
  background: #f9f9f9;
  color: #333;
  min-height: 100vh;
  display: grid;
  grid-template-rows: auto 1fr auto;
}

.home-navbar {
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 15px 30px;
}

.home-navbar-content {
  display: flex;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  gap: 60px;
  position: relative;
}

.home-logo {
  font-size: 22px;
  font-weight: bold;
  text-decoration: none;
  color: #333;
  display: flex;
  align-items: center;
}

.home-logo img {
  height: 46px;
  transition: transform 0.3s ease;
}

.home-logo:hover img {
  transform: scale(1.05);
}

.home-logo .green {
  color: #64b100;
}

.home-nav {
  display: grid;
  grid-auto-flow: column;
  justify-content: center;
  align-items: center;
  top: 100%;
  left: 0;
  right: 0;
  gap: 50px;
  margin: 0 auto;
  text-align: center;
}

.home-nav a:not(.home-logout) {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
  padding: 8px 15px;
  white-space: nowrap;
}

.home-nav a:not(.home-logout):hover,
.home-nav a.active {
  color: #64b100;
}

.home-nav a.active {
  font-weight: 600;
}

.nav-link {
  color: #888;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #000;
}

.nav-link.active {
  color: #000;
  font-weight: 600;
  border-bottom: 2px solid #000;
}

.user-profile-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: auto;
  position: relative;
  justify-self: end;
}

.home-logout {
  color: #e74c3c;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-right: 10px;
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 500;
  text-decoration: none;
  border: 1px solid transparent;
  transition: all 0.3s ease;
}

.home-logout:hover {
  background: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.3);
}

.home-logout i {
  font-size: 14px;
  color: #e74c3c;
}

.home-profile-icon {
  width: 42px;
  height: 42px;
  background: #64b100;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border: 2px solid #fff;
  margin-left: 40px;
}

.home-profile-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.hero {
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
    url("../public/images/Hero-Home (1).png") no-repeat center/cover;
  color: #fff;
  padding: 60px 20px;
  display: grid;
  grid-template-rows: auto auto auto;
  align-content: center;
  justify-items: left;
  gap: 20px;
  margin-bottom: 5rem;
}

.hero-content {
  margin-left: 90px;
}

.hero h1 {
  font-size: 2.5rem;
  margin: 0 0 10px;
}

.hero p {
  font-size: 1rem;
  max-width: 700px;
  line-height: 1.6;
  margin-bottom: 0.5em;
}

.hero p + p {
  margin-top: 0;
}

.hero .green {
  color: #a9e652
}

.hero-buttons {
  display: grid;
  grid-template-columns: max-content max-content;
  gap: 20px;
  margin-top: 20px;
}

.hero button {
  padding: 10px 20px;
  font-size: 1rem;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: normal;
}

.hero-buttons button.green {
  background-color: #a9e652;
  color: #1f1f1f;
}

.hero-buttons button.green:hover {
  background-color: #548f00;
  transform: translateY(-2px);
}

.hero-buttons button.white {
  background-color: #fff;
  color: #080808;

}

.hero-buttons button.white:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
}

.main-activities {
  border: 1px solid #ccc;
  border-radius: 20px;
  padding: 2rem;
  background-color: #fff;
  margin: 2rem auto;
  max-width: 1300px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: bold;
}

.logo-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #a9e652;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-circle img {
  width: 30px;
  height: 30px;
}

hr {
  width: 100%;
  border: none;
  border-top: 1px solid #ccc;
  margin: 2rem 0;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  max-width: 1200px;
  margin: auto;
  padding: 0 2rem;
  box-sizing: border-box;
}

.card {
  position: relative;
  display: grid;
  grid-template-rows: auto auto 1fr auto;
  background-color: #eeecec;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  padding: 20px;
  min-height: 220px;
  width: auto;
  margin-top: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 12px;
  justify-self: start;
}

.card h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #222;
  line-height: 1.3;
}

.card p {
  font-size: 0.9rem;
  color: #555;
  margin: 0 0 auto 0;
  line-height: 1.5;
  flex-grow: 1;
}

.card button {
  background-color: #000;
  color: white;
  border: none;
  padding: 10px 18px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.3s ease;
  justify-self: end;
  align-self: end;
  margin-top: 16px;
  width: fit-content;
}

.card button:hover {
  background-color: #333;
}

.home-footer {
  background-color: #a9e652;
  color: #000000;
  text-align: center;
  padding: 15px;
  font-size: 14px;
}

.home-menu-toggle {
  display: none;
  font-size: 24px;
  color: #64b100;
  cursor: pointer;
  justify-self: end;
  margin-left: auto;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  z-index: 1001;
}

.home-menu-toggle:hover {
  background-color: rgba(100, 177, 0, 0.1);
  transform: scale(1.1);
}

.home-menu-toggle.active {
  color: #a9e652;
  background-color: rgba(100, 177, 0, 0.15);
}

.home-menu-toggle i {
  transition: transform 0.3s ease;
}

.home-menu-toggle.active i {
  transform: rotate(90deg);
}

.home-nav.show {
  display: grid !important;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu-backdrop.show {
  opacity: 1;
  visibility: visible;
}

.grid-container-home {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* Hanya 2 kolom */
  gap: 1.5rem;
  padding: 1.5rem;
}

.card-home {
  background-color: #f7f7f7;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: transform 0.3s ease;
}

.card-home:hover {
  transform: translateY(-4px);
}


.card-home button {
  padding: 8px 40px;
  font-size: 0.8rem;
  background-color: #1e1e1e; /* hitam */
  color: #ffffff;
  border: none;
  border-radius: 6px;
  margin-top: 1rem;
  cursor: pointer;
  align-self: flex-end; /* tombol tidak full lebar, hanya selebar konten */
  transition: background-color 0.2s ease;
}

.card-home button:hover {
  background-color: #333333; /* lebih terang sedikit saat hover */
}

@media (max-width: 480px) {
  .home-navbar-content {
    grid-template-columns: auto 1fr auto;
    position: relative;
  }

  .hero-content {
  margin: auto;
}

    .grid-container-home {
    grid-template-columns: 1fr; /* 1 kolom di layar kecil */
  }

  .home-nav {
    grid-column: 1 / -1;
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    grid-template-columns: 1fr;
    text-align: center;
    background: #fff;
    padding: 16px 0;
    border-radius: 0 0 10px 10px;
    gap: 12px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    margin-top: 8px;
  }

  .home-menu-toggle {
    display: block;
  }

  .hero {
    padding: 30px 16px;
  }

  .hero h1 {
    font-size: 1.5rem;
  }

  .hero-buttons {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .grid-container {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

    .grid-container-home {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 0px;
  }

  .card {
    min-height: 160px;
    padding: 14px;
  }

  .card-icon,
  .card-icon-placeholder {
    width: 36px;
    height: 36px;
    margin-bottom: 8px;
  }

  .card h3 {
    font-size: 0.95rem;
  }

  .card p {
    font-size: 0.8rem;
  }

  .user-profile-container {
    position: absolute;
    top: 56px;
    right: 16px;
    flex-direction: column-reverse;
    background: #fff;
    padding: 8px;
    border-radius: 6px;
    gap: 6px;
    display: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 100;
  }

  .home-nav.show ~ .user-profile-container {
    display: flex;
  }

  .home-logout {
    width: 100%;
    justify-content: center;
    order: 2;
  }

  .home-profile-icon {
    order: 1;
    margin: 0 auto;
  }

    .card-home button {
    width: 100%;        /* tombol selebar card */
    align-self: center; }

}

@media (max-width: 768px) {
  .home-navbar-content {
    grid-template-columns: auto 1fr auto;
    position: relative;
  }
    .grid-container-home {
    grid-template-columns: 1fr; /* 1 kolom di layar kecil */
  }

  .hero-content {
  margin:auto;
}

  .home-nav {
    display: none;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    grid-template-columns: 1fr;
    text-align: center;
    background: #fff;
    padding: 20px 0;
    border-radius: 0 0 12px 12px;
    gap: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    margin-top: 10px;
  }

  .home-nav.show {
    display: flex !important;
    animation: slideDown 0.3s ease;
  }

  .home-menu-toggle {
    display: block;
  }

  .hero {
    padding: 40px 20px;
  }

  .hero h1 {
    font-size: 1.8rem;
  }

  .hero-buttons {
    grid-template-columns: 1fr;
  }

  .grid-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card {
    min-height: 180px;
    padding: 16px;
  }

  .card-icon,
  .card-icon-placeholder {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
  }

  .card h3 {
    font-size: 1rem;
  }

  .card p {
    font-size: 0.85rem;
  }

  .card button {
    padding: 8px 14px;
    font-size: 0.85rem;
  }

  .user-profile-container {
    position: absolute;
    top: 60px;
    right: 20px;
    flex-direction: column-reverse;
    background: #fff;
    padding: 10px;
    border-radius: 8px;
    gap: 8px;
    display: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    z-index: 100;
  }

  .home-nav.show ~ .user-profile-container {
    display: flex;
  }

  .home-logout {
    width: 100%;
    justify-content: center;
    order: 2;
  }

  .home-profile-icon {
    order: 1;
    margin: 0 auto;
  }
}
