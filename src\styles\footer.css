.main-footer {
  display: grid;
  grid-template-columns: 1fr;
  align-items: center;
  background-image: url("../public/images/Footer-LandingPage.png");
  background-size: cover;
  background-position: center;
  color: white;
  padding: 4rem 2rem;
  position: relative;
}

.main-footer::after {
  content: "";
  position: absolute;
  inset: 0;
  background-color: rgba(0, 80, 0, 0.3);
  z-index: 0;
}

.main-footer .cta-text {
  z-index: 1;
  text-align: center;
  padding: 5px;
}

.main-footer .cta-text h2 {
  font-size: 28px;
  margin-bottom: 16px;
}

.main-footer .cta-text p {
  font-size: 16px;
  margin-bottom: 24px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.main-footer .cta-text button {
  margin: 0.5rem;
  padding: 10px 30px;
  border: none;
  border-radius: 999px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.main-footer .cta-text .outline {
  background: transparent;
  border: 2px solid white;
  color: white;
}

.main-footer .cta-text .outline:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.footer-bottom {
  background-color: #64b100;
  color: #ffffff;
  text-align: center;
  padding: 15px;
  font-size: 14px;
}

@media (max-width: 992px) {
  .main-footer {
    padding: 3rem 1.5rem;
  }

  .main-footer .cta-text h2 {
    font-size: 26px;
  }
}

@media (max-width: 768px) {
  .main-footer {
    padding: 2.5rem 1.5rem;
  }

  .main-footer .cta-text h2 {
    font-size: 24px;
  }

  .main-footer .cta-text p {
    font-size: 15px;
    margin-bottom: 20px;
  }

  .main-footer .cta-text button {
    padding: 8px 24px;
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .main-footer {
    padding: 2rem 1rem;
  }

  .main-footer .cta-text h2 {
    font-size: 22px;
  }

  .main-footer .cta-text p {
    font-size: 14px;
  }

  .cta-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .main-footer .cta-text button {
    margin: 0.25rem;
    width: 150px;
  }

  .footer-bottom {
    padding: 12px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .main-footer .cta-text h2 {
    font-size: 20px;
  }

  .main-footer .cta-text p {
    font-size: 13px;
  }

  .main-footer .cta-text button {
    font-size: 13px;
    padding: 8px 20px;
  }
}

.main-footer .cta-text button:not(.outline) {
  background-color: white;
  color: green;
}

.main-footer .cta-text button:not(.outline):hover {
  background-color: #b6bdb6;
}

.footer-bottom {
  background-color: #64b100;
  text-align: center;
  padding: 1rem;
  color: #ffffff;
}
