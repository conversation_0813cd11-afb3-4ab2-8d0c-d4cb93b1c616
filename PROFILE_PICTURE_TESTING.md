# Profile Picture Testing Guide

## Issue Diagnosis

The profile pictures are not displaying because of several potential issues:

1. **User Data Missing Profile Picture URL** - The user might not have uploaded a profile picture yet
2. **API Response Structure** - The profile picture URL might be in a different field than expected
3. **URL Generation Issues** - The profile picture URL might not be generated correctly
4. **Authentication Issues** - The user data might not be properly stored or retrieved

## Testing Steps

### Step 1: Check User Data Structure

1. Open the browser developer console (F12)
2. Navigate to any page with the navigation bar (e.g., profile page)
3. Look for console logs that show:
   - `ProfilePage render - userData from authService:`
   - `ProfilePage render - profilePictureUrl:`
   - `NavigationBar createProfileIcon:`

### Step 2: Use Debug Tool

1. Open `debug-profile-picture.html` in your browser
2. Click each test button to see:
   - **Check User Data**: Shows current user data structure
   - **Test URL Generation**: Tests URL generation with different inputs
   - **Test Navigation Bar**: Tests navigation bar profile icon creation
   - **Test Profile Picture Service**: Tests the profile picture service methods
   - **Test with Mock Data**: Tests with fake profile picture data
   - **Test API Endpoint**: Tests the actual API endpoint

### Step 3: Upload a Profile Picture

1. Go to the profile page (`#/profile`)
2. Click "Edit Photo" button
3. Select an image file (max 2MB)
4. Click "Save Profile & Upload Picture"
5. Check console logs for:
   - `Upload profile picture selesai, updated user data:`
   - `Refreshed user data from API after profile picture upload`
   - `Navigation bar profile picture updated`

### Step 4: Check API Response

1. Open browser Network tab
2. Navigate to profile page
3. Look for API calls to:
   - `/api/auth/user` - Should return user data with profilePictureUrl
   - `/api/account/profile-picture/{filename}` - Should return the actual image

## Expected Behavior

### When User Has No Profile Picture
- Navigation bar should show user's initial in a colored circle
- Profile page avatars should show fallback avatars with user initials
- Console should log: "No profile picture URL, using fallback initial"

### When User Has Profile Picture
- Navigation bar should show the actual profile picture
- Profile page avatars should show the uploaded image
- Console should log the generated profile picture URL
- If image fails to load, should fallback to initials

### After Uploading Profile Picture
- Profile picture should immediately appear in profile page
- Navigation bar should update to show the new profile picture
- User data should be refreshed from API

## Common Issues and Solutions

### Issue 1: User Data is Null/Undefined
**Symptoms**: Console shows `userData: null` or `userData: undefined`
**Solution**: User needs to login properly, check authentication

### Issue 2: Profile Picture URL is Null
**Symptoms**: Console shows `profilePictureUrl: null`
**Solution**: User needs to upload a profile picture first

### Issue 3: Profile Picture URL is Invalid
**Symptoms**: Console shows URL but image fails to load
**Solution**: Check if the API endpoint is accessible, verify URL format

### Issue 4: Navigation Bar Not Updating
**Symptoms**: Profile picture uploads but navigation bar still shows initials
**Solution**: Check if `updateNavigationBarProfilePicture()` is being called

## Debug Console Commands

You can run these commands in the browser console for manual testing:

```javascript
// Check current user data
const authService = await import('./src/scripts/data/auth-service.js');
console.log('User Data:', authService.default.getUserData());

// Test profile picture URL generation
const profilePictureService = await import('./src/scripts/data/profile-picture-service.js');
console.log('Generated URL:', profilePictureService.default.getProfilePictureUrl('test.jpg'));

// Test navigation bar creation
const { NavigationBar } = await import('./src/scripts/components/NavigationBar.js');
const navbar = new NavigationBar({
  currentPath: '/test',
  userInitial: 'T',
  username: 'TestUser',
  profilePictureUrl: 'test.jpg',
  showProfile: true
});
console.log('Profile Icon HTML:', navbar.createProfileIcon());
```

## API Testing

Test the profile picture API endpoints directly:

### Get Current User
```
GET /api/auth/user
Authorization: Bearer {your-token}
```

### Upload Profile Picture
```
POST /api/account/profile-picture
Authorization: Bearer {your-token}
Content-Type: multipart/form-data
Body: profilePicture={image-file}
```

### Get Profile Picture
```
GET /api/account/profile-picture/{filename}
```

## Expected Console Output

When everything works correctly, you should see logs like:

```
ProfilePage render - userData from authService: {id: "...", username: "...", profilePictureUrl: "..."}
ProfilePictureService.getProfilePictureUrl input: profile-123.jpg
ProfilePictureService.getProfilePictureUrl: Generated URL: https://apiww-production.up.railway.app/api/account/profile-picture/profile-123.jpg
NavigationBar createProfileIcon: {profilePictureUrl: "profile-123.jpg", username: "...", userInitial: "..."}
Generated profile picture URL: https://apiww-production.up.railway.app/api/account/profile-picture/profile-123.jpg
```

## Troubleshooting

1. **Clear browser cache** - Old cached data might interfere
2. **Check network connectivity** - Ensure API server is accessible
3. **Verify authentication** - Make sure user is properly logged in
4. **Check file permissions** - Ensure uploaded files are accessible
5. **Review API logs** - Check server-side logs for errors

## Next Steps

After testing, you should be able to determine:
1. Whether user data contains profile picture information
2. Whether URL generation is working correctly
3. Whether the API endpoints are accessible
4. Whether the navigation bar update mechanism works

Based on the test results, we can implement targeted fixes for any issues found.
