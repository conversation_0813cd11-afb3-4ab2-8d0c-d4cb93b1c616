<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Profile Picture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .debug-output {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .profile-demo {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        .app-profile-icon {
            width: 42px;
            height: 42px;
            background: #64b100;
            color: #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 2px solid #fff;
        }
        .app-profile-icon.profile-picture {
            background: none;
            object-fit: cover;
            padding: 0;
        }
        .app-profile-icon.fallback-icon {
            background: #64b100;
        }
        .app-profile-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>Profile Picture Debug Tool</h1>
        
        <div class="debug-section">
            <h3>1. Current User Data</h3>
            <button onclick="checkUserData()">Check User Data</button>
            <div id="user-data-output" class="debug-output"></div>
        </div>

        <div class="debug-section">
            <h3>2. Profile Picture URL Generation</h3>
            <button onclick="testUrlGeneration()">Test URL Generation</button>
            <div id="url-generation-output" class="debug-output"></div>
        </div>

        <div class="debug-section">
            <h3>3. Navigation Bar Profile Icon</h3>
            <button onclick="testNavigationBar()">Test Navigation Bar</button>
            <div id="navbar-output" class="debug-output"></div>
            <div class="profile-demo" id="navbar-demo"></div>
        </div>

        <div class="debug-section">
            <h3>4. Profile Picture Service Test</h3>
            <button onclick="testProfilePictureService()">Test Profile Picture Service</button>
            <div id="service-output" class="debug-output"></div>
        </div>

        <div class="debug-section">
            <h3>5. Mock Profile Picture Test</h3>
            <button onclick="testWithMockData()">Test with Mock Data</button>
            <div id="mock-output" class="debug-output"></div>
            <div class="profile-demo" id="mock-demo"></div>
        </div>

        <div class="debug-section">
            <h3>6. API Test</h3>
            <button onclick="testApiEndpoint()">Test API Endpoint</button>
            <div id="api-output" class="debug-output"></div>
        </div>
    </div>

    <script type="module">
        // Import necessary modules
        let authService, profilePictureService, NavigationBar, CONFIG;

        try {
            const modules = await Promise.all([
                import('./src/scripts/data/auth-service.js'),
                import('./src/scripts/data/profile-picture-service.js'),
                import('./src/scripts/components/NavigationBar.js'),
                import('./src/scripts/config.js')
            ]);
            
            authService = modules[0].default;
            profilePictureService = modules[1].default;
            NavigationBar = modules[2].NavigationBar;
            CONFIG = modules[3].default;
            
            console.log('All modules loaded successfully');
        } catch (error) {
            console.error('Error loading modules:', error);
        }

        // Make functions available globally
        window.checkUserData = function() {
            const output = document.getElementById('user-data-output');
            try {
                const userData = authService.getUserData();
                const token = authService.getToken();
                const isAuthenticated = authService.isAuthenticated();
                
                output.textContent = JSON.stringify({
                    isAuthenticated,
                    token: token ? token.substring(0, 20) + '...' : null,
                    userData: userData
                }, null, 2);
            } catch (error) {
                output.textContent = 'Error: ' + error.message;
            }
        };

        window.testUrlGeneration = function() {
            const output = document.getElementById('url-generation-output');
            try {
                const testCases = [
                    null,
                    undefined,
                    '',
                    'profile-123.jpg',
                    '/api/account/profile-picture/test.jpg',
                    'https://example.com/image.jpg',
                    'test-image.png'
                ];
                
                let results = [];
                testCases.forEach(testCase => {
                    const result = profilePictureService.getProfilePictureUrl(testCase);
                    results.push(`Input: ${JSON.stringify(testCase)} → Output: ${JSON.stringify(result)}`);
                });
                
                output.textContent = results.join('\n');
            } catch (error) {
                output.textContent = 'Error: ' + error.message;
            }
        };

        window.testNavigationBar = function() {
            const output = document.getElementById('navbar-output');
            const demo = document.getElementById('navbar-demo');
            
            try {
                const userData = authService.getUserData();
                
                const navbar = new NavigationBar({
                    currentPath: '/test',
                    userInitial: 'T',
                    username: userData?.username || 'TestUser',
                    profilePictureUrl: userData?.profilePictureUrl,
                    showProfile: true
                });
                
                const profileIcon = navbar.createProfileIcon();
                
                output.textContent = JSON.stringify({
                    userData: userData,
                    profilePictureUrl: userData?.profilePictureUrl,
                    generatedIcon: profileIcon
                }, null, 2);
                
                demo.innerHTML = profileIcon;
            } catch (error) {
                output.textContent = 'Error: ' + error.message;
            }
        };

        window.testProfilePictureService = function() {
            const output = document.getElementById('service-output');
            try {
                const userData = authService.getUserData();
                const profilePictureUrl = userData?.profilePictureUrl;
                
                const fullUrl = profilePictureService.getProfilePictureUrl(profilePictureUrl);
                const fallbackHtml = profilePictureService.createFallbackAvatar('TestUser', 'medium');
                const profileElement = profilePictureService.createProfilePictureElement(
                    profilePictureUrl, 
                    userData?.username || 'TestUser', 
                    'medium'
                );
                
                output.textContent = JSON.stringify({
                    input: profilePictureUrl,
                    fullUrl: fullUrl,
                    fallbackHtml: fallbackHtml,
                    profileElement: profileElement
                }, null, 2);
            } catch (error) {
                output.textContent = 'Error: ' + error.message;
            }
        };

        window.testWithMockData = function() {
            const output = document.getElementById('mock-output');
            const demo = document.getElementById('mock-demo');
            
            try {
                const mockProfilePictureUrl = 'mock-profile.jpg';
                
                const navbar = new NavigationBar({
                    currentPath: '/test',
                    userInitial: 'M',
                    username: 'MockUser',
                    profilePictureUrl: mockProfilePictureUrl,
                    showProfile: true
                });
                
                const profileIcon = navbar.createProfileIcon();
                
                output.textContent = JSON.stringify({
                    mockProfilePictureUrl: mockProfilePictureUrl,
                    generatedUrl: navbar.getProfilePictureUrl(mockProfilePictureUrl),
                    generatedIcon: profileIcon
                }, null, 2);
                
                demo.innerHTML = profileIcon;
            } catch (error) {
                output.textContent = 'Error: ' + error.message;
            }
        };

        window.testApiEndpoint = async function() {
            const output = document.getElementById('api-output');
            try {
                const userData = authService.getUserData();
                const token = authService.getToken();
                
                if (!token) {
                    output.textContent = 'No authentication token found. Please login first.';
                    return;
                }
                
                // Test getting current user
                const currentUser = await authService.getCurrentUser();
                
                output.textContent = JSON.stringify({
                    storedUserData: userData,
                    apiUserData: currentUser,
                    profilePictureUrl: currentUser?.profilePictureUrl,
                    baseUrl: CONFIG.BASE_URL
                }, null, 2);
            } catch (error) {
                output.textContent = 'Error: ' + error.message + '\n\nStack: ' + error.stack;
            }
        };
    </script>
</body>
</html>
