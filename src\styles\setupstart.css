@import url("https://fonts.googleapis.com/css2?family=Poppins&display=swap");

* {
  box-sizing: border-box;
}

body,
html {
  margin: 0;
  padding: 0;
  font-family: "Poppins", sans-serif;
  background-color: #f7f9fc;
  height: 100%;
}

.setup-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 1rem;
  background-color: #ffffff;
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.setup-card {
  background: #fff;
  border-radius: 20px;
  box-shadow: 6px 10px 18px rgba(0, 0, 0, 0.15);
  padding: 2.5rem 2rem;
  max-width: 500px;
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  user-select: none;
}

.title {
  font-size: 1.6rem;
  line-height: 1.3;
  margin-bottom: 0.5rem;
  color: #222;
  font-weight: 600;
}

.title .green {
  color: #28a745;
}

.desc {
  font-size: 0.875rem;
  color: #666;
  font-weight: 400;
}

#form-step {
  display: block;
  width: 100%;
  text-align: left;
}

#setup-form label {
  font-size: 0.9rem;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 0;
}

#setup-form input[type="text"]{
  margin-top: 2rem;
}

#setup-form input[type="text"],
#setup-form select {
  width: 100%;
  padding: 10px 15px;
  border-radius: 15px;
  background-color: #efefef;
  border: none;
  font-family: "Poppins", sans-serif;
  font-size: 0.9rem;
  outline: none;
  transition: background-color 0.3s ease;
  margin-bottom: 16px;
}

#setup-form select {
  appearance: none;
  -webkit-appearance: none; 
  -moz-appearance: none; 
  
  background-image: url("data:image/svg+xml;utf8,<svg fill='gray' height='25' viewBox='0 0 24 24' width='25' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>");
  background-repeat: no-repeat;
  background-position: right 12px center; 
  background-size: 16px;

  padding-right: 40px; 
}


.experience-options label {
  display: inline-block !important;
  margin-right: 19px;
  margin-top: 10px;
  cursor: pointer;
  user-select: none;
  justify-content: center !important;
  align-items: center !important;
}

#setup-form .experience-options {
  margin-bottom: 16px;
}

#setup-form input[type="text"]:focus,
#setup-form select:focus {
  background-color: #e0e0e0;
}

#setup-form input[type="radio"] {
  margin-right: 8px;
  cursor: pointer;
  vertical-align: middle;
}

#setup-form .radio-group label {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  cursor: pointer;
  color: #333;
}

#setup-form button {
  width: 100%;
  padding: 9px;
  background-color: #a9e652;
  color: rgb(65, 65, 65);
  border: none;
  border-radius: 30px;
  cursor: pointer;
  font-family: "Poppins", sans-serif;
  font-size: 1rem;
  transition: background-color 0.3s ease;
  margin-top: 0.8rem;
}

#setup-form button:hover {
  background-color: #8ccf3a;
}

#map {
  width: 100% !important;
  height: 200px;
  overflow: hidden;
  background-color: #eee;
  margin-top: 10px;
}

.ol-control button {
  background-color: white !important;
  border-left: 1px solid #ccc !important;
  border-right: 1px solid #ccc !important;
  border-top: 1px solid #ccc !important;
  border-bottom: none !important;
  color: black !important;
  width: 30px !important;
  height: 30px !important;
  font-weight: bold;
  font-size: 20px;
  line-height: 1;
  padding: 0;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  border-radius: 0 !important;
  margin: 0 !important;
  cursor: pointer;
  outline: none;
  transition: background-color 0.3s;
}

.ol-zoom button:last-child {
  border-bottom: 1px solid #ccc !important;
}

.ol-zoom {
  display: flex !important;
  flex-direction: column;
  gap: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.ol-zoom {
  gap: 0 !important;
}

.ol-control button:hover {
  background-color: #f0f0f0 !important;
}

.ol-control button:hover {
  background-color: #f0f0f0 !important;
}

.dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ccc;
  transition: background-color 0.3s ease, width 0.3s ease, height 0.3s ease;
  margin-top: 20px;
}

.dot.active {
  background-color: #525252;
  width: 10px;
  height: 10px;
}

button#next-btn {
  background: none;
  color: rgb(31, 31, 31);
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  align-self: center;
  min-width: 120px;
  user-select: none;
  margin-left: auto;
  margin-top: 1rem;
}

.ol-popup {
  position: absolute;
  background-color: white;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid #ccc;
  min-width: 300px;
  text-align: center;
  transform: translate(-50%, -100%);
  box-shadow: 0 1px 4px rgba(0,0,0,0.2);
  font-size: 12px; /* ukuran font diperkecil */
  line-height: 1.3;
}

.ol-popup::after {
  content: "";
  position: absolute;
  bottom: -10px; 
  left: 50%;
  transform: translateX(-50%);
  border-width: 10px 10px 0;
  border-style: solid;
  border-color: white transparent transparent;
}

@media (max-width: 480px) {
  .setup-card {
    padding: 2rem 1.5rem;
    max-width: 100%;
  }

  .icon {
    font-size: 3.5rem;
  }

  .title {
    font-size: 1.4rem;
  }

  #setup-form button,
  button#next-btn {
    font-size: 0.9rem;
    padding: 0.65rem 1.5rem;
  }
}
