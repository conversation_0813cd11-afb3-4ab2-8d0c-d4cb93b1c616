<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .profile-demo {
            display: flex;
            gap: 20px;
            align-items: center;
            margin-top: 15px;
        }
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #ddd;
        }
        .fallback-avatar {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            border-radius: 50%;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>CORS Fix Test for Profile Pictures</h1>
        
        <div class="test-section">
            <h3>1. Test Profile Picture Service with Authentication</h3>
            <p>This test will try to fetch the profile picture using the new CORS-safe method.</p>
            <button onclick="testProfilePictureService()">Test Profile Picture Service</button>
            <div id="service-result" class="test-result info" style="display: none;"></div>
            <div class="profile-demo">
                <img id="test-avatar" class="avatar" style="display: none;">
                <div id="test-fallback" class="fallback-avatar" style="display: none;">PZ</div>
            </div>
        </div>

        <div class="test-section">
            <h3>2. Test Navigation Bar Profile Picture</h3>
            <p>This test will create a navigation bar with profile picture support.</p>
            <button onclick="testNavigationBar()">Test Navigation Bar</button>
            <div id="navbar-result" class="test-result info" style="display: none;"></div>
            <div id="navbar-demo"></div>
        </div>

        <div class="test-section">
            <h3>3. Test Direct Image Fetch</h3>
            <p>This test will try to fetch the profile picture directly from the API.</p>
            <button onclick="testDirectFetch()">Test Direct Fetch</button>
            <div id="fetch-result" class="test-result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. Current User Data</h3>
            <p>Shows the current user data and profile picture URL.</p>
            <button onclick="showUserData()">Show User Data</button>
            <div id="user-data-result" class="test-result info" style="display: none;"></div>
        </div>
    </div>

    <script type="module">
        // Import necessary modules
        let authService, profilePictureService, NavigationBar;

        try {
            const modules = await Promise.all([
                import('./src/scripts/data/auth-service.js'),
                import('./src/scripts/data/profile-picture-service.js'),
                import('./src/scripts/components/NavigationBar.js')
            ]);
            
            authService = modules[0].default;
            profilePictureService = modules[1].default;
            NavigationBar = modules[2].NavigationBar;
            
            console.log('All modules loaded successfully');
        } catch (error) {
            console.error('Error loading modules:', error);
        }

        // Test functions
        window.testProfilePictureService = async function() {
            const resultDiv = document.getElementById('service-result');
            const testAvatar = document.getElementById('test-avatar');
            const testFallback = document.getElementById('test-fallback');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.textContent = 'Testing profile picture service...';

            try {
                const userData = authService.getUserData();
                
                if (!userData || !userData.profilePictureUrl) {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = 'No user data or profile picture URL found. Please login and upload a profile picture first.';
                    testFallback.style.display = 'flex';
                    return;
                }

                // Test the new updateImageElement method
                await profilePictureService.updateImageElement(testAvatar, userData.profilePictureUrl, userData.username);
                
                resultDiv.className = 'test-result success';
                resultDiv.textContent = `✓ Profile picture service test completed!\nUser: ${userData.username}\nProfile Picture URL: ${userData.profilePictureUrl}`;
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ Error: ${error.message}\n\nStack: ${error.stack}`;
                testFallback.style.display = 'flex';
            }
        };

        window.testNavigationBar = async function() {
            const resultDiv = document.getElementById('navbar-result');
            const demoDiv = document.getElementById('navbar-demo');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.textContent = 'Testing navigation bar...';

            try {
                const userData = authService.getUserData();
                
                const navbar = new NavigationBar({
                    currentPath: '/test',
                    userInitial: userData?.username?.charAt(0).toUpperCase() || 'T',
                    username: userData?.username || 'TestUser',
                    profilePictureUrl: userData?.profilePictureUrl,
                    showProfile: true
                });
                
                demoDiv.innerHTML = navbar.render();
                
                // Bind events and load profile picture
                setTimeout(async () => {
                    navbar.bindEvents();
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = `✓ Navigation bar created successfully!\nProfile Picture URL: ${userData?.profilePictureUrl || 'None'}`;
                }, 100);
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ Error: ${error.message}`;
            }
        };

        window.testDirectFetch = async function() {
            const resultDiv = document.getElementById('fetch-result');
            
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result info';
            resultDiv.textContent = 'Testing direct fetch...';

            try {
                const userData = authService.getUserData();
                
                if (!userData || !userData.profilePictureUrl) {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = 'No profile picture URL to test with.';
                    return;
                }

                const fullUrl = profilePictureService.getProfilePictureUrl(userData.profilePictureUrl);
                
                // Test the fetchImageWithAuth method
                const imageBlob = await profilePictureService.fetchImageWithAuth(fullUrl);
                
                if (imageBlob) {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = `✓ Successfully fetched image!\nURL: ${fullUrl}\nBlob size: ${imageBlob.size} bytes\nBlob type: ${imageBlob.type}`;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = `✗ Failed to fetch image from: ${fullUrl}`;
                }
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ Error: ${error.message}`;
            }
        };

        window.showUserData = function() {
            const resultDiv = document.getElementById('user-data-result');
            
            resultDiv.style.display = 'block';
            
            try {
                const userData = authService.getUserData();
                const token = authService.getToken();
                
                resultDiv.className = 'test-result info';
                resultDiv.textContent = JSON.stringify({
                    isAuthenticated: authService.isAuthenticated(),
                    hasToken: !!token,
                    userData: userData
                }, null, 2);
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `Error: ${error.message}`;
            }
        };
    </script>
</body>
</html>
