.container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 2rem;
}

.logout-container {
  max-width: 500px;
  width: 100%;
  padding: 3rem;
  margin: 0 auto;
  text-align: center;
  border-radius: 20px;
  background: #ffffff;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.logout-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.logout-container h1 {
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #333;
  font-size: 2rem;
}

.logout-container p {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.loader {
  border: 5px solid #f3f3f3;
  border-radius: 50%;
  border-top: 5px solid #64b100;
  width: 60px;
  height: 60px;
  margin: 0 auto;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .logout-container {
    padding: 2rem;
    max-width: 90%;
  }

  .logout-container h1 {
    font-size: 1.8rem;
  }

  .logout-container p {
    font-size: 1rem;
  }

  .loader {
    width: 50px;
    height: 50px;
  }
}
