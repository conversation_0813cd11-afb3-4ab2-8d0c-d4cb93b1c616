.gallery-carousel {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
  padding: 20px 0;
}

.gallery-carousel-track-wrapper {
  width: 100%;
  max-width: 800px;
  overflow: hidden;
  position: relative;
}

.gallery-carousel-track {
  display: flex;
  transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.gallery-slide {
  flex: 0 0 150px;
  margin: 0 5px;
  transition: all 0.5s ease;
  transform: scale(0.8);
  opacity: 0.6;
  filter: brightness(0.7);
}

.gallery-slide img {
  width: 100%;
  height: 150px;
  border-radius: 10px;
  object-fit: cover;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.5s ease;
}

.gallery-slide.active {
  transform: scale(1.1);
  opacity: 1;
  filter: brightness(1);
  z-index: 2;
}

.gallery-slide.active img {
  box-shadow: 0 8px 20px rgba(43, 147, 72, 0.3);
  border: 2px solid #2b9348;
}

.gallery-slide.dimmed {
  transform: scale(0.85);
  opacity: 0.7;
  filter: brightness(0.8);
}

.gallery-carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(43, 147, 72, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 3;
  font-size: 16px;
}

.gallery-carousel-nav:hover {
  background-color: #2b9348;
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 4px 12px rgba(43, 147, 72, 0.4);
}

.gallery-carousel-left {
  left: 10px;
}

.gallery-carousel-right {
  right: 10px;
}

.learning-page {
  font-family: "Poppins", sans-serif;
  background-color: #f9f9f9;
  min-height: 100vh;
}

.learning-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.learning-content h2 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 20px;
  color: #333;
}

.learning-content .highlight {
  color: #2b9348;
}

.learning-section {
  margin-top: 40px;
}

.learning-section h3 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 10px;
}

.learning-main {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 30px;
  margin-bottom: 40px;
}

.learning-content-area {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.learning-search {
  position: relative;
  margin-bottom: 20px;
}

.learning-search input {
  width: 100%;
  padding: 12px 40px 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s ease;
}

.learning-search input:focus {
  border-color: #2b9348;
}

.learning-search .search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 16px;
}

.learning-grid {
  display: grid;
  gap: 16px;
}

.learning-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.learning-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.learning-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.learning-item h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 10px;
}

.learning-item-description {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.favorite-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  color: #ccc;
}

.favorite-btn:hover {
  background-color: #f0f0f0;
}

.favorite-btn.favorite-active {
  color: #e74c3c;
}

.favorite-btn i {
  font-size: 16px;
}

.learning-filter-panel {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.learning-filter-panel h4 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.learning-filter-panel h4 i {
  color: #2b9348;
}

.filter-group {
  margin-bottom: 20px;
}

.filter-group strong {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-size: 14px;
}

.filter-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group input[type="checkbox"] {
  margin: 0;
  accent-color: #2b9348;
}

.learning-filter-panel hr {
  border: none;
  border-top: 1px solid #e0e0e0;
  margin: 20px 0;
}

.favorite-filter-btn {
  background: none;
  border: none;
  color: #2b9348;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  transition: color 0.3s ease;
}

.favorite-filter-btn:hover {
  color: #1e6b32;
}

.favorite-filter-btn.active {
  background-color: #2b9348;
  color: white;
  border-radius: 4px;
  padding: 8px 12px;
}

.clear-filters-btn {
  background: none;
  border: none;
  color: #e74c3c;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  transition: color 0.3s ease;
  margin-top: 10px;
}

.clear-filters-btn:hover {
  color: #c0392b;
}

.article-title {
  cursor: pointer;
  transition: color 0.3s ease;
}

.article-title:hover {
  color: #2b9348;
}

.video-section {
  margin: 40px 0;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.video-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.video-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 180px;
  overflow: hidden;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  transition: all 0.3s ease;
}

.video-item:hover .video-play-overlay {
  background: rgba(43, 147, 72, 0.9);
  transform: translate(-50%, -50%) scale(1.1);
}

.video-content {
  padding: 16px;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.video-content h4 {
  margin: 0;
  font-size: 16px;
  color: #333;
  line-height: 1.4;
  flex: 1;
  margin-right: 10px;
}

.video-description {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.video-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.video-modal-content {
  position: relative;
  width: 90%;
  max-width: 800px;
  aspect-ratio: 16/9;
}

.video-modal-content iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 8px;
}

.video-modal-close {
  position: absolute;
  top: -40px;
  right: 0;
  background: none;
  border: none;
  color: white;
  font-size: 30px;
  cursor: pointer;
  padding: 5px;
}

.recent-learning {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recent-learning-content {
  display: grid;
  gap: 20px;
}

.recent-article a {
  color: #2b9348;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
}

.recent-article a:hover {
  text-decoration: underline;
}

.recent-video-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.recent-video-item {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.recent-video-thumbnail {
  position: relative;
  width: 120px;
  height: 80px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
}

.recent-video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recent-video-thumbnail .video-play-overlay {
  width: 30px;
  height: 30px;
  font-size: 12px;
}

.recent-video-content h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
  line-height: 1.4;
}

.recent-video-content p {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.no-recent-learning {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-recent-learning p {
  margin: 8px 0;
  font-size: 14px;
}

.recent-article-item,
.recent-video-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.recent-article-item:hover,
.recent-video-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.recent-article-content h5,
.recent-video-content h5 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
  line-height: 1.4;
}

.recent-article-content p,
.recent-video-content p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.recent-timestamp {
  font-size: 12px;
  color: #999;
  font-style: italic;
}

.recent-video-item {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.recent-video-thumbnail {
  position: relative;
  width: 120px;
  height: 80px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
}

.recent-video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recent-video-thumbnail .video-play-overlay {
  width: 30px;
  height: 30px;
  font-size: 12px;
}

.page-footer {
  background-color: #a9e652;
  color: #000000;
  text-align: center;
  padding: 15px;
  font-size: 14px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .gallery-carousel {
    margin: 15px 0;
    padding: 15px 0;
  }

  .gallery-carousel-track-wrapper {
    max-width: 90%;
  }

  .gallery-slide {
    flex: 0 0 120px;
    margin: 0 3px;
  }

  .gallery-slide img {
    height: 120px;
  }

  .gallery-slide.active {
    transform: scale(1.05);
  }

  .gallery-slide.dimmed {
    transform: scale(0.9);
  }

  .gallery-carousel-nav {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }

  .gallery-carousel-left {
    left: 5px;
  }

  .gallery-carousel-right {
    right: 5px;
  }

  .learning-main {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .learning-filter-panel {
    order: -1;
  }

  .video-grid {
    grid-template-columns: 1fr;
  }

  .recent-video-item {
    flex-direction: column;
    gap: 10px;
  }

  .recent-video-thumbnail {
    width: 100%;
    height: 150px;
  }

  .learning-content {
    padding: 15px;
  }

  .learning-content h2 {
    font-size: 1.5rem;
  }

  .learning-section h3 {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .gallery-slide {
    flex: 0 0 100px;
    margin: 0 2px;
  }

  .gallery-slide img {
    height: 100px;
  }

  .gallery-carousel-nav {
    width: 30px;
    height: 30px;
    font-size: 12px;
  }

  .learning-content {
    padding: 10px;
  }

  .learning-content-area,
  .learning-filter-panel,
  .recent-learning {
    padding: 15px;
  }

  .video-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .video-item {
    margin: 0;
  }

  .video-thumbnail {
    height: 150px;
  }

  .filter-group {
    margin-bottom: 15px;
  }

  .learning-item {
    padding: 12px;
  }

  .learning-item h4 {
    font-size: 14px;
  }

  .learning-item-description {
    font-size: 13px;
  }

  .video-modal-content {
    width: 95%;
    margin: 0 10px;
  }

  .video-modal-close {
    top: -35px;
    font-size: 25px;
  }
}
