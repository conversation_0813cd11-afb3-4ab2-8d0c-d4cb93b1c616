.navbar {
  background-color: rgb(255, 255, 255, 0.3);
  padding: 10px 30px;
  margin: 40px;
  height: 70px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
}

.navbar input[type="text"] {
  padding: 10px 16px;
  border-radius: 25px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.9);
  outline: none;
  width: 300px;
  font-size: 14px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar input[type="text"]:focus {
  border-color: #7ac142;
  background-color: white;
  box-shadow: 0 4px 12px rgba(122, 193, 66, 0.2);
  transform: translateY(-1px);
}

.navbar input[type="text"]::placeholder {
  color: #666;
  font-style: italic;
}

.logo {
  height: 150px;
  width: auto;
  object-fit: contain;
  cursor: pointer;
}

.nav-links {
  list-style: none;
  display: flex;
  gap: 20px;
  margin-right: 20px;
}

.nav-links li a {
  text-decoration: none;
  color: white;
  font-weight: 500;
}

.nav-links li a:hover {
  color: #b6bdb6;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.hamburger-menu {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  cursor: pointer;
  z-index: 1001;
}

.bar {
  height: 3px;
  width: 100%;
  background-color: white;
  border-radius: 10px;
  transition: all 0.3s ease-in-out;
}

.hamburger-menu.active .bar:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
}

.hamburger-menu.active .bar:nth-child(2) {
  opacity: 0;
}

.hamburger-menu.active .bar:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
}

@media (max-width: 992px) {
  .navbar {
    padding: 10px 20px;
    margin: 20px;
  }

  .navbar input[type="text"] {
    width: 220px;
    padding: 8px 14px;
    font-size: 13px;
  }

  .logo {
    height: 120px;
  }
}

@media (max-width: 768px) {
  .hamburger-menu {
    display: flex;
  }

  .nav-right {
    position: fixed;
    top: 0;
    right: -100%;
    flex-direction: column;
    background-color: rgba(76, 154, 91, 0.95);
    width: 70%;
    height: 100vh;
    padding: 80px 20px 20px;
    transition: right 0.3s ease-in-out;
    z-index: 999;
  }

  .nav-right.active {
    right: 0;
  }

  .nav-links {
    flex-direction: column;
    width: 100%;
    margin: 0 0 30px 0;
  }

  .nav-links li {
    margin: 15px 0;
    text-align: center;
  }

  .search-form {
    width: 100%;
  }

  .navbar input[type="text"] {
    width: 100%;
    padding: 12px 16px;
    font-size: 16px;
    border-radius: 20px;
  }

  .logo {
    height: 100px;
  }
}

@media (max-width: 480px) {
  .navbar {
    padding: 10px 15px;
    margin: 10px;
    height: 60px;
  }

  .logo {
    height: 80px;
  }

  .nav-right {
    width: 85%;
  }
}

.nav-right form input {
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid #ccc;
}
