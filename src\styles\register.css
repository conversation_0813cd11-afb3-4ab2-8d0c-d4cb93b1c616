@import url('https://fonts.googleapis.com/css2?family=Poppins&display=swap');

.container {
  display: flex;
  justify-content: center; 
  align-items: center;     
  height: 100vh;          
  background-color: #ffffff; 
  padding: 1rem;
  box-sizing: border-box;
  font-family: 'Poppins', sans-serif;
}

.form-label {
  margin-bottom: 0.25rem;
  display: block;
  font-family: 'Poppins', sans-serif;
  font-size: 0.9rem;
  color: #333;
  line-height: 1.4;
  word-break: break-word;
}


.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  border: 0;
}

.form-label {
  margin-bottom: 0.25rem;
  display: block;
  font-family: 'Poppins', sans-serif;
  font-size: 0.9rem;
  color: #333;
}

.register-container {
  max-width: 450px;
  width: 100%; 
  padding: 3rem;
  border-radius: 20px;
  background: #ffffff;
  box-shadow: 6px 10px 18px rgba(0, 0, 0, 0.232);

}

.register-container h1 {
  text-align: center;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.register-subtitle {
  font-size: 0.85rem;
  text-align: center;
  color: #666666;
  margin-bottom: 1.5rem;
  font-weight: 400;
}

.register-container form {
  display: flex;
  flex-direction: column;
}

.register-container input {
  padding: 9px 20px;  
  border-radius: 15px;
  background-color: #EFEFEF;
  border: none;
  font-family: 'Poppins', sans-serif;
  font-size: 0.85rem;   
  outline: none;
  transition: background-color 0.3s ease;
}


.register-container input:focus {
  background-color: #e0e0e0;
}

.register-container button {
  padding: 9px;
  background-color: #A9E652;
  color: #1E1E1E;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  font-family: 'Poppins', sans-serif;
  font-size: 0.85rem;  
  transition: background-color 0.3s ease;
}

.register-container button:hover {
  background-color: #8ccf3a;
}

.login-link {
  text-align: center;
  margin-top: 1rem;
  font-size: 0.9rem;
  color: #444444;
  font-family: 'Poppins', sans-serif;
}

.login-link a {
  color: #2483FF;
  text-decoration: none;
}

.error {
  color: red;
  font-size: 0.75rem;
  font-family: 'Poppins', sans-serif;
  min-height: 1rem;        
  margin-top: 0;         
  margin-bottom: 0;  
  overflow: hidden;
  transition: margin 0.3s ease;
  display: block;
}

.password-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.password-wrapper input {
  width: 100%;
}

.toggle-password {
  position: absolute;
  right: 8px;
  background: none !important;
  border: none;
  cursor: pointer;
  color: #969696 !important;
  font-size: 0.85rem; 
  padding: 0;
  display: flex;
  align-items: center;
}

.toggle-password i {
  font-size: 0.8rem; 
  transition: all 0.2s ease;
}



.error:not(:empty) {
  margin-top: 0.3rem;
  margin-bottom: 0.5rem;
}




