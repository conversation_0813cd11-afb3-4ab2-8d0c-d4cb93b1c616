.hero-section {
  background-color: #4c9a5b;
  color: white;
  text-align: center;
  padding: 5px 0 40px 0;
  background-image: url("../public/images/Hero-LandingPage.png");
  background-size: cover;
  background-position: center;
  background-blend-mode: multiply;
  position: relative;
}

.hero-content {
  margin-left: 90px;
  max-width: 700px;
  text-align: left;
  padding: 20px 0;
}

.hero-content h1 {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 16px;
}

.hero-content h1 span {
  color: #d0d331;
}

.hero-content p {
  font-size: 18px;
  margin-bottom: 30px;
  color: #eee;
}

.hero-content button {
  background-color: #ffffff;
  color: #247928;
  padding: 10px 30px;
  border-radius: 999px;
  border: none;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.hero-content button:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Media Queries for Hero Section */
@media (max-width: 992px) {
  .hero-content {
    margin-left: 60px;
    max-width: 600px;
  }

  .hero-content h1 {
    font-size: 32px;
  }

  .hero-content p {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 5px 0 30px 0;
  }

  .hero-content {
    margin: 0 auto;
    padding: 30px 20px;
    text-align: center;
    max-width: 90%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .hero-content h1 {
    font-size: 28px;
    margin-bottom: 20px;
  }

  .hero-content p {
    font-size: 16px;
    margin-bottom: 25px;
  }

  .hero-content button {
    padding: 12px 28px;
    font-size: 16px;
    margin-top: 10px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 5px 0 20px 0;
  }

  .hero-content {
    padding: 25px 15px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .hero-content h1 {
    font-size: 24px;
    margin-bottom: 18px;
    line-height: 1.3;
  }

  .hero-content p {
    font-size: 14px;
    margin-bottom: 22px;
    line-height: 1.5;
  }

  .hero-content button {
    padding: 10px 24px;
    font-size: 15px;
    margin-top: 8px;
  }
}

.hero-content button:hover {
  background-color: #b6bdb6;
}

.tagline {
  display: flex;
  justify-content: space-around;
  background-color: #32cd32;
  padding: 20px 40px;
  font-weight: bold;
  color: #080808;
  flex-wrap: wrap;
  gap: 10px;
}
