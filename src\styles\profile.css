.container-profile {
  width: 100%;
  max-width: 500px;
  align-items: center;
  margin: 2rem auto;
  padding: 2rem;
  border: 1px solid #ccc;
  border-radius: 10px;
  font-family: "Poppins", sans-serif;
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  text-align: start;
  gap: 40px;
}

.profile-page-container {
  height: 100vh; /* atau 100% jika parent-nya sudah punya height */
  display: flex;
  flex-direction: column;
}

.profile-container {
  flex: 1; /* supaya memenuhi sisa ruang antara navbar dan footer */
  display: flex;
}


main {
  flex-grow: 1;
  background-color: #fff;
  padding: 20px;
}
.profile-avatar-wrapper {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.avatar-left img {
  width: 5rem;
  height: 5rem;
  object-fit: cover;
  border-radius: 50%;
  margin-top: 10px;
}

.edit-right {
  display: flex;
  flex-direction: column;
}

.avatar-note {
  font-size: 0.875rem;
  color: #666;
  margin-top: 0.5rem;
}

.profile-title {
  font-size: 20px;
  font-weight: bold;
}

.divider {
  border: none;
  border-top: 1px solid #ccc;
  margin-bottom: 1rem;
}

.profile-form {
  display: flex;
  flex-direction: column;
  align-items: start;
}

.profile-avatar-wrapper {
  margin-top: 20px;
}

.profile-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1rem;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 0.5rem;
}

.edit-photo-btn {
  background-color: #eaeaea;
  color: #333;
  padding: 0.3rem 1rem;
  font-size: 14px;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 0.3rem;
  display: inline-block;
}

.photo-note {
  font-size: 11px;
  color: #777;
  line-height: 1.3;
}

.input-group {
  width: 100%;
  text-align: left;
  margin-top: 0.5rem;
}

.input-group label {
  display: block;
  font-size: 14px;
  margin-bottom: 0.2rem;
}

.input-group input[type="text"] {
  width: 100%;
  padding: 0.5rem;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 6px;
}

.experience-options {
  display: flex;
  justify-content: space-between;
}

.experience-options label {
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding-top: 0.2rem !important;
}

.experience-options input[type="radio"] {
  accent-color: #a9e652;
}

.edit-profile-btn {
  margin-top: 1.5rem;
  padding: 0.7rem 1.5rem;
  background-color: #969696;
  color: white;
  border: none;
  font-size: 14px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.profile-footer {
  background-color: #a9e652 !important;
  color: #282828;
  padding: 1rem;
  text-align: center;
}

.edit-profile-btn:hover {
  background-color: #6cb117;
}

#editAvatarBtn {
  padding: 0.5rem 1rem;
  background-color: #f4f4f4; /* Hijau lembut */
  color: rgb(63, 63, 63);
  border: none;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: fit-content;
}

.container-profile h1 {
  text-align: center;
}

#editAvatarBtn:hover {
  background-color: #cfcfcf;
}

.profile-actions {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

#saveProfileBtn {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: #a9e652;
  color: rgb(40, 40, 40);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  box-sizing: border-box;
}

.container-profile label {
  font-size: 0.9rem;
}

.profile-experience {
  margin-top: 0.8rem;
}

.avatar-note {
  font-size: 0.75rem;
  color: #666;
  margin-top: 3px;
  max-width: 60%;
}

.profile-container {
  display: flex;
    height: 100%!important;
}

.profile-sidebar {
  width: 250px;
  background-color: #ffffff;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 7rem !important;
  height: 100%!important;
}

.sidebar-avatar img.avatar {
  width: 150px!important;
  height: 150px!important;
  border-radius: 50%;
  object-fit: cover;
  align-items: center;
}

.sidebar-avatar p {
  text-align: center;
}

#sidebarUsername {
  font-weight: bold;
}

#sidebarExperience {
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.sidebar-button {
  margin-top: 10px;
  background-color: #222;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

/* ========== RESPONSIVE ========== */
@media (max-width: 1550px) {
  .profile-container {
    flex-direction: column;
    align-items: center;
  }

  .profile-sidebar {
    width: 100%;
    max-height: auto!important;
    max-width: 300px!important;
    flex-direction: row;
    justify-content: center; /* ganti dari flex-start */
    border-right: none;
    border-bottom: 1px solid #ccc;
    padding: 1rem;
    gap: 1rem;
    text-align: center;
  }

  .sidebar-avatar {
    flex-direction: row;
    align-items: center;
    justify-content: center; /* tambahkan ini */
    gap: 1rem;
    width: 100%;
  }

  .sidebar-avatar img.avatar {
    width: 60px;
    height: 60px;
    margin: 0 auto; /* tengahin gambar */
  }

  .sidebar-avatar p {
    text-align: center;
    margin: 0 auto; /* biar teksnya ga nempel kiri */
  }

  .sidebar-button {
    margin-top: 0;
    margin-left: 0; /* reset auto */
    margin-right: 0;
    height: fit-content;
    align-self: center; /* pastikan button center */
  }

  .container-profile {
    max-width: 90%;
    padding: 1rem;
  }

  .profile-avatar-wrapper {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .experience-options {
    flex-direction: column;
    gap: 0.5rem;
  }

  #saveProfileBtn {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {

  .profile-sidebar {
    width: 100%;
    max-width: none!important;
  }
    main{
    width: 100%!important;
    max-width: none!important;
  }
    .profile-sidebar {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding-top: 2rem !important;
    width: 100%!important;
    max-width: none!important;
  }
}

/* ========== TABLET (768px – 1023px) ========== */
@media (min-width: 768px) {
  .profile-container {
    flex-direction: row;
    align-items: flex-start;
  }

  main{
    width: 100%!important;
    max-width: none!important;
  }

  .profile-sidebar {
    width: 100%;
    flex-direction: column;
    justify-content: flex-start;
    border-right: 1px solid #ccc;
    border-bottom: none;
    padding: 2rem 1rem;
    gap: 2rem;
  }

  .sidebar-avatar {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .sidebar-avatar img.avatar {
    width: 80px;
    height: 80px;
    margin-left: auto;
    margin-right: auto;
    align-items: center;
  }

  .sidebar-avatar p {
    text-align: center;
  }

  .sidebar-button {
    margin-top: auto;
    margin-left: 0;
  }

  .container-profile {
    max-width: 600px;
    padding: 2rem;
  }

  .profile-avatar-wrapper {
    flex-direction: row;
    align-items: center;
    gap: 2rem;
  }

  .experience-options {
    flex-direction: row;
    gap: 1rem;
  }

  #saveProfileBtn {
    font-size: 1rem;
  }
}

@media (max-width: 600px) {
  .home-nav {
    display: none;
  }

  .home-menu-toggle {
    display: block;
  }

  .profile-sidebar {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding-top: 2rem !important;
    width: 100%!important;
    max-width: none!important;
  }

  .sidebar-avatar {
    flex-direction: column;
    align-items: center;
  }

  .sidebar-button {
    width: 100%;
    margin-top: 1rem;
  }

  .container-profile {
    width: 95%;
    padding: 1rem;
  }

  .profile-avatar-wrapper {
    flex-direction: column;
    align-items: center;
  }

  .input-group input[type="text"] {
    font-size: 0.9rem;
  }

  #editAvatarBtn,
  #saveProfileBtn {
    width: 100%;
  }

  .avatar-note {
    text-align: center;
    max-width: 100%;
  }
}
